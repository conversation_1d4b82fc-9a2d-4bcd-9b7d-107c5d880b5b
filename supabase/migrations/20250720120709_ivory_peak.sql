/*
  # Create Tarkeeb Database Schema

  1. New Tables
    - `profiles` - User profiles extending Supabase auth
    - `services` - Available home services
    - `bookings` - Service bookings
    - `technician_services` - Many-to-many relationship for technician skills
    - `ratings` - Service ratings and reviews
    - `technician_availability` - Technician working hours

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Separate policies for customers and technicians
*/

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('customer', 'technician');
CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled');
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'wallet');

-- Profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  name text NOT NULL,
  phone text,
  role user_role NOT NULL DEFAULT 'customer',
  avatar_url text,
  rating numeric(3,2) DEFAULT 0,
  total_ratings integer DEFAULT 0,
  is_available boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name_ar text NOT NULL,
  name_en text NOT NULL,
  description_ar text,
  description_en text,
  category text NOT NULL,
  base_price numeric NOT NULL,
  duration_minutes integer NOT NULL DEFAULT 60,
  icon text NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Technician services (many-to-many)
CREATE TABLE IF NOT EXISTS technician_services (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  technician_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  service_id uuid REFERENCES services(id) ON DELETE CASCADE,
  custom_price numeric,
  created_at timestamptz DEFAULT now(),
  UNIQUE(technician_id, service_id)
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  technician_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  service_id uuid REFERENCES services(id) ON DELETE CASCADE NOT NULL,
  address text NOT NULL,
  notes text,
  scheduled_date date NOT NULL,
  scheduled_time time NOT NULL,
  status booking_status DEFAULT 'pending',
  total_price numeric NOT NULL,
  payment_method payment_method DEFAULT 'cash',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Ratings table
CREATE TABLE IF NOT EXISTS ratings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  customer_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  technician_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  rating integer CHECK (rating BETWEEN 1 AND 5) NOT NULL,
  comment text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(booking_id)
);

-- Technician availability table
CREATE TABLE IF NOT EXISTS technician_availability (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  technician_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  day_of_week integer CHECK (day_of_week BETWEEN 0 AND 6) NOT NULL, -- 0 = Sunday
  start_time time NOT NULL,
  end_time time NOT NULL,
  created_at timestamptz DEFAULT now(),
  UNIQUE(technician_id, day_of_week)
);

-- Insert sample services
INSERT INTO services (name_ar, name_en, description_ar, description_en, category, base_price, duration_minutes, icon) VALUES
('تركيب التلفزيون', 'TV Installation', 'تركيب وتوصيل التلفزيون على الحائط', 'Professional TV mounting and setup', 'electronics', 150, 60, 'tv'),
('صيانة المكيف', 'AC Maintenance', 'تنظيف وصيانة أجهزة التكييف', 'Air conditioning cleaning and maintenance', 'appliances', 200, 90, 'wind'),
('تجميع الأثاث', 'Furniture Assembly', 'تجميع وتركيب قطع الأثاث', 'Professional furniture assembly service', 'furniture', 100, 120, 'wrench'),
('السباكة', 'Plumbing', 'إصلاح وصيانة الأنابيب والحنفيات', 'Pipe and faucet repair services', 'maintenance', 180, 75, 'droplets'),
('الكهرباء', 'Electrical Work', 'إصلاح وتركيب الأعمال الكهربائية', 'Electrical installation and repair', 'maintenance', 160, 60, 'zap'),
('تنظيف المنزل', 'House Cleaning', 'تنظيف شامل للمنزل', 'Complete house cleaning service', 'cleaning', 120, 180, 'home'),
('صيانة الغسالة', 'Washing Machine Repair', 'إصلاح وصيانة الغسالات', 'Washing machine repair and maintenance', 'appliances', 170, 90, 'settings'),
('دهان الجدران', 'Wall Painting', 'دهان وتجديد الجدران', 'Professional wall painting service', 'renovation', 300, 240, 'paintbrush');

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE technician_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE technician_availability ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view all profiles" ON profiles FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE TO authenticated USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);

-- Services policies
CREATE POLICY "Anyone can view active services" ON services FOR SELECT TO authenticated USING (is_active = true);

-- Technician services policies
CREATE POLICY "Anyone can view technician services" ON technician_services FOR SELECT TO authenticated USING (true);
CREATE POLICY "Technicians can manage own services" ON technician_services FOR ALL TO authenticated USING (
  technician_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'technician')
);

-- Bookings policies
CREATE POLICY "Users can view own bookings" ON bookings FOR SELECT TO authenticated USING (
  customer_id = auth.uid() OR technician_id = auth.uid()
);
CREATE POLICY "Customers can create bookings" ON bookings FOR INSERT TO authenticated WITH CHECK (
  customer_id = auth.uid() AND 
  customer_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'customer')
);
CREATE POLICY "Users can update own bookings" ON bookings FOR UPDATE TO authenticated USING (
  customer_id = auth.uid() OR technician_id = auth.uid()
);

-- Ratings policies
CREATE POLICY "Anyone can view ratings" ON ratings FOR SELECT TO authenticated USING (true);
CREATE POLICY "Customers can create ratings for their bookings" ON ratings FOR INSERT TO authenticated WITH CHECK (
  customer_id = auth.uid() AND 
  booking_id IN (SELECT id FROM bookings WHERE customer_id = auth.uid() AND status = 'completed')
);

-- Technician availability policies
CREATE POLICY "Anyone can view technician availability" ON technician_availability FOR SELECT TO authenticated USING (true);
CREATE POLICY "Technicians can manage own availability" ON technician_availability FOR ALL TO authenticated USING (
  technician_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'technician')
);

-- Functions
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, name, role)
  VALUES (new.id, COALESCE(new.raw_user_meta_data->>'name', 'User'), 'customer');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update profile updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();