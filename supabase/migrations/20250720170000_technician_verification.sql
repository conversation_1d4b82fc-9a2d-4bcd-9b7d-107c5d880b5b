/*
  # Technician Verification and Onboarding System
  
  1. Add verification status and onboarding fields to profiles
  2. Create technician_documents table for ID uploads
  3. Create technician_onboarding table for onboarding progress
  4. Update profiles table with additional technician fields
*/

-- Add verification and onboarding fields to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'under_review', 'approved', 'rejected'));
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS experience_years INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS national_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS address TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS city TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Create technician_documents table for document uploads
CREATE TABLE IF NOT EXISTS technician_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  technician_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('national_id_front', 'national_id_back', 'certificate', 'portfolio')),
  file_url TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified BOOLEAN DEFAULT false,
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES profiles(id)
);

-- Create technician_onboarding table to track onboarding progress
CREATE TABLE IF NOT EXISTS technician_onboarding (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  technician_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  step_personal_info BOOLEAN DEFAULT false,
  step_services BOOLEAN DEFAULT false,
  step_documents BOOLEAN DEFAULT false,
  step_experience BOOLEAN DEFAULT false,
  step_verification BOOLEAN DEFAULT false,
  current_step INTEGER DEFAULT 1,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_technician_documents_technician_id ON technician_documents(technician_id);
CREATE INDEX IF NOT EXISTS idx_technician_documents_type ON technician_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_technician_onboarding_technician_id ON technician_onboarding(technician_id);
CREATE INDEX IF NOT EXISTS idx_profiles_verification_status ON profiles(verification_status);

-- Enable RLS on new tables
ALTER TABLE technician_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE technician_onboarding ENABLE ROW LEVEL SECURITY;

-- RLS Policies for technician_documents
CREATE POLICY "Technicians can view own documents" 
  ON technician_documents FOR SELECT 
  TO authenticated 
  USING (technician_id = auth.uid());

CREATE POLICY "Technicians can insert own documents" 
  ON technician_documents FOR INSERT 
  TO authenticated 
  WITH CHECK (technician_id = auth.uid());

CREATE POLICY "Technicians can update own documents" 
  ON technician_documents FOR UPDATE 
  TO authenticated 
  USING (technician_id = auth.uid())
  WITH CHECK (technician_id = auth.uid());

-- RLS Policies for technician_onboarding
CREATE POLICY "Technicians can view own onboarding" 
  ON technician_onboarding FOR SELECT 
  TO authenticated 
  USING (technician_id = auth.uid());

CREATE POLICY "Technicians can insert own onboarding" 
  ON technician_onboarding FOR INSERT 
  TO authenticated 
  WITH CHECK (technician_id = auth.uid());

CREATE POLICY "Technicians can update own onboarding" 
  ON technician_onboarding FOR UPDATE 
  TO authenticated 
  USING (technician_id = auth.uid())
  WITH CHECK (technician_id = auth.uid());

-- Function to create onboarding record when technician profile is created
CREATE OR REPLACE FUNCTION create_technician_onboarding()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.role = 'technician' AND OLD.role IS DISTINCT FROM 'technician' THEN
    INSERT INTO technician_onboarding (technician_id)
    VALUES (NEW.id)
    ON CONFLICT (technician_id) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create onboarding record
DROP TRIGGER IF EXISTS trigger_create_technician_onboarding ON profiles;
CREATE TRIGGER trigger_create_technician_onboarding
  AFTER UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_technician_onboarding();

-- Function to update onboarding completion status
CREATE OR REPLACE FUNCTION update_onboarding_completion()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if all steps are completed
  IF NEW.step_personal_info AND NEW.step_services AND NEW.step_documents AND NEW.step_experience THEN
    NEW.completed_at = NOW();
    NEW.step_verification = true;
    
    -- Update profile onboarding status
    UPDATE profiles 
    SET onboarding_completed = true,
        verification_status = 'under_review'
    WHERE id = NEW.technician_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update completion status
DROP TRIGGER IF EXISTS trigger_update_onboarding_completion ON technician_onboarding;
CREATE TRIGGER trigger_update_onboarding_completion
  BEFORE UPDATE ON technician_onboarding
  FOR EACH ROW
  EXECUTE FUNCTION update_onboarding_completion();

-- Update the handle_new_user function to use role from metadata
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, name, role)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email, 'User'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'customer')::TEXT
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
