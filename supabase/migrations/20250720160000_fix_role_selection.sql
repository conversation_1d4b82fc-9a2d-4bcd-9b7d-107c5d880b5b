/*
  # Fix Role Selection Flow
  
  1. Update the handle_new_user function to not set a default role
  2. Allow NULL roles temporarily during signup
  3. Update RLS policies to handle users without roles
*/

-- Update the profiles table to allow NULL roles temporarily
ALTER TABLE profiles ALTER COLUMN role DROP NOT NULL;

-- Update the handle_new_user function to not set a default role
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, name, role)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email, 'User'),
    NULL  -- Don't set a default role, let user choose
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to handle users without roles
DROP POLICY IF EXISTS "Users can view all profiles" ON profiles;
CREATE POLICY "Users can view all profiles"
  ON profiles FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Update bookings policies to handle users without roles
DROP POLICY IF EXISTS "Customers can create bookings" ON bookings;
CREATE POLICY "Customers can create bookings" 
  ON bookings FOR INSERT 
  TO authenticated 
  WITH CHECK (
    customer_id = auth.uid() AND 
    customer_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'customer')
  );

-- Update technician services policies
DROP POLICY IF EXISTS "Technicians can manage own services" ON technician_services;
CREATE POLICY "Technicians can manage own services" 
  ON technician_services FOR ALL 
  TO authenticated 
  USING (
    technician_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'technician')
  );

-- Update ratings policies
DROP POLICY IF EXISTS "Customers can create ratings for their bookings" ON ratings;
CREATE POLICY "Customers can create ratings for their bookings" 
  ON ratings FOR INSERT 
  TO authenticated 
  WITH CHECK (
    customer_id = auth.uid() AND 
    booking_id IN (SELECT id FROM bookings WHERE customer_id = auth.uid() AND status = 'completed')
  );

-- Update technician availability policies
DROP POLICY IF EXISTS "Technicians can manage own availability" ON technician_availability;
CREATE POLICY "Technicians can manage own availability" 
  ON technician_availability FOR ALL 
  TO authenticated 
  USING (
    technician_id IN (SELECT id FROM profiles WHERE id = auth.uid() AND role = 'technician')
  );
