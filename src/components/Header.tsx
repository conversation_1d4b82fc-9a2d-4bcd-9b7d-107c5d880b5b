import React from 'react';
import { Globe } from 'lucide-react';
import { useApp } from '../contexts/AppContext';

const Header: React.FC = () => {
  const { language, setLanguage, isRTL } = useApp();

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">ت</span>
          </div>
          <h1 className="text-xl font-bold text-gray-900">
            {language === 'ar' ? 'تركيب' : 'Tarkeeb'}
          </h1>
        </div>
        
        <button
          onClick={toggleLanguage}
          className="flex items-center space-x-2 space-x-reverse px-3 py-1.5 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <Globe className="w-4 h-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">
            {language === 'ar' ? 'EN' : 'عر'}
          </span>
        </button>
      </div>
    </header>
  );
};

export default Header;