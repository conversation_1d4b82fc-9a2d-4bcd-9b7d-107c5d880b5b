import React from 'react';
import { Tv, Wind, Wrench, Droplets, Zap, Home, Settings, Paintbrush } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { Service } from '../lib/supabase';

interface ServiceCardProps {
  service: Service;
  onSelect: (serviceId: string) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, onSelect }) => {
  const { language } = useApp();

  const getIcon = (iconName: string) => {
    const icons = {
      tv: Tv,
      wind: Wind,
      wrench: Wrench,
      droplets: Droplets,
      zap: Zap,
      home: Home,
      settings: Settings,
      paintbrush: Paintbrush,
    };
    const Icon = icons[iconName as keyof typeof icons] || Wrench;
    return <Icon className="w-6 h-6" />;
  };

  return (
    <button
      onClick={() => onSelect(service.id)}
      className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md hover:border-blue-200 transition-all w-full text-right service-card"
    >
      <div className="flex items-center justify-between mb-3">
        <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center text-blue-600">
          {getIcon(service.icon)}
        </div>
        <div className="flex-1 mr-3">
          <h3 className="font-semibold text-gray-900 text-right">
            {language === 'ar' ? service.name_ar : service.name_en}
          </h3>
          {service.description_ar && service.description_en && (
            <p className="text-sm text-gray-600 text-right mt-1">
              {language === 'ar' ? service.description_ar : service.description_en}
            </p>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span className="bg-yellow-50 text-yellow-700 px-2 py-1 rounded-full">
          {service.base_price} {language === 'ar' ? 'ج.م' : 'EGP'}
        </span>
        <span>
          {service.duration_minutes} {language === 'ar' ? 'دقيقة' : 'min'}
        </span>
      </div>
    </button>
  );
};

export default ServiceCard;