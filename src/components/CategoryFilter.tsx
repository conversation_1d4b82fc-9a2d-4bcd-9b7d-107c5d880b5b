import React from 'react';
import { Tv, Wind, Wrench, Droplets, Zap, Grid3X3 } from 'lucide-react';
import { useApp } from '../contexts/AppContext';

interface CategoryFilterProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({ selectedCategory, onCategoryChange }) => {
  const { language } = useApp();

  const categories = [
    {
      id: 'all',
      nameAr: 'الكل',
      nameEn: 'All',
      icon: Grid3X3,
    },
    {
      id: 'electronics',
      nameAr: 'الإلكترونيات',
      nameEn: 'Electronics',
      icon: Tv,
    },
    {
      id: 'appliances',
      nameAr: 'الأجهزة',
      nameEn: 'Appliances',
      icon: Wind,
    },
    {
      id: 'furniture',
      nameAr: 'الأثاث',
      nameEn: 'Furniture',
      icon: Wrench,
    },
    {
      id: 'maintenance',
      nameAr: 'الصيانة',
      nameEn: 'Maintenance',
      icon: Droplets,
    },
  ];

  return (
    <div className="flex space-x-2 space-x-reverse overflow-x-auto pb-2">
      {categories.map((category) => {
        const Icon = category.icon;
        const isSelected = selectedCategory === category.id;
        
        return (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={`flex flex-col items-center space-y-1 p-3 rounded-xl min-w-20 transition-all ${
              isSelected
                ? 'bg-blue-600 text-white shadow-md'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
            }`}
          >
            <Icon className="w-5 h-5" />
            <span className="text-xs font-medium whitespace-nowrap">
              {language === 'ar' ? category.nameAr : category.nameEn}
            </span>
          </button>
        );
      })}
    </div>
  );
};

export default CategoryFilter;