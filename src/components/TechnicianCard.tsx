import React from 'react';
import { Star, Phone } from 'lucide-react';
import { useApp } from '../contexts/AppContext';

interface TechnicianCardProps {
  technician: {
    id: string;
    name: string;
    phone: string;
    rating: number;
    avatar: string;
    services: string[];
    available: boolean;
  };
  compact?: boolean;
}

const TechnicianCard: React.FC<TechnicianCardProps> = ({ technician, compact = false }) => {
  const { language } = useApp();

  if (compact) {
    return (
      <div className="flex items-center space-x-3 space-x-reverse">
        <img
          src={technician.avatar}
          alt={technician.name}
          className="w-10 h-10 rounded-full object-cover"
        />
        <div className="flex-1">
          <h4 className="font-medium text-gray-900">{technician.name}</h4>
          <div className="flex items-center space-x-1 space-x-reverse">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="text-sm text-gray-600">{technician.rating}</span>
          </div>
        </div>
        <a
          href={`tel:${technician.phone}`}
          className="p-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <Phone className="w-4 h-4" />
        </a>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
      <div className="flex items-center space-x-3 space-x-reverse mb-3">
        <img
          src={technician.avatar}
          alt={technician.name}
          className="w-12 h-12 rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{technician.name}</h3>
          <div className="flex items-center space-x-1 space-x-reverse">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="text-sm text-gray-600">{technician.rating}</span>
            <span className="text-sm text-gray-400">
              ({language === 'ar' ? 'تقييم' : 'rating'})
            </span>
          </div>
        </div>
        <div className={`w-3 h-3 rounded-full ${technician.available ? 'bg-green-400' : 'bg-gray-300'}`} />
      </div>
      
      <div className="text-sm text-gray-600">
        <span className={technician.available ? 'text-green-600' : 'text-gray-500'}>
          {technician.available 
            ? (language === 'ar' ? 'متاح الآن' : 'Available now')
            : (language === 'ar' ? 'غير متاح' : 'Unavailable')
          }
        </span>
      </div>
    </div>
  );
};

export default TechnicianCard;