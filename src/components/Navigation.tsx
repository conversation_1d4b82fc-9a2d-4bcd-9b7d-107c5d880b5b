import React from 'react';
import { Home, Calendar, User, Settings } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { useLocation, useNavigate } from 'react-router-dom';

const Navigation: React.FC = () => {
  const { language, user, profile } = useApp();
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    {
      icon: Home,
      labelAr: 'الرئيسية',
      labelEn: 'Home',
      path: '/',
    },
    {
      icon: Calendar,
      labelAr: 'حجوزاتي',
      labelEn: 'Bookings',
      path: '/bookings',
    },
    {
      icon: User,
      labelAr: profile?.role === 'technician' ? 'لوحة التحكم' : 'الملف الشخصي',
      labelEn: profile?.role === 'technician' ? 'Dashboard' : 'Profile',
      path: profile?.role === 'technician' ? '/technician' : '/profile',
    },
    {
      icon: Settings,
      labelAr: 'الإعدادات',
      labelEn: 'Settings',
      path: '/settings',
    },
  ];

  const handleNavigation = (path: string) => {
    if (!user) {
      navigate('/auth');
      return;
    }
    navigate(path);
  };

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50">
      <div className="flex justify-around items-center">
        {navItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <button
              key={index}
              onClick={() => handleNavigation(item.path)}
              className={`flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors ${
                isActive 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="text-xs font-medium">
                {language === 'ar' ? item.labelAr : item.labelEn}
              </span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default Navigation;