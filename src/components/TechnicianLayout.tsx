import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Home, 
  Calendar, 
  User, 
  Settings, 
  Bell,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';

interface TechnicianLayoutProps {
  children: React.ReactNode;
}

const TechnicianLayout: React.FC<TechnicianLayoutProps> = ({ children }) => {
  const { language, profile } = useApp();
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    {
      id: 'dashboard',
      icon: Home,
      label: { ar: 'الرئيسية', en: 'Dashboard' },
      path: '/technician/dashboard'
    },
    {
      id: 'jobs',
      icon: Calendar,
      label: { ar: 'الطلبات', en: 'Jobs' },
      path: '/technician/jobs'
    },
    {
      id: 'analytics',
      icon: BarChart3,
      label: { ar: 'التقارير', en: 'Analytics' },
      path: '/technician/analytics'
    },
    {
      id: 'profile',
      icon: User,
      label: { ar: 'الملف الشخصي', en: 'Profile' },
      path: '/technician/profile'
    }
  ];

  const getVerificationStatusBadge = () => {
    if (!profile) return null;

    const statusConfig = {
      pending: {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: Clock,
        text: { ar: 'قيد المراجعة', en: 'Under Review' }
      },
      under_review: {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: Clock,
        text: { ar: 'قيد المراجعة', en: 'Under Review' }
      },
      approved: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        text: { ar: 'معتمد', en: 'Verified' }
      },
      rejected: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: AlertCircle,
        text: { ar: 'مرفوض', en: 'Rejected' }
      }
    };

    const config = statusConfig[profile.verification_status];
    const Icon = config.icon;

    return (
      <div className={`flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-lg border text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        <span>{config.text[language]}</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">T</span>
              </div>
              <div>
                <h1 className="font-bold text-gray-900">
                  {language === 'ar' ? 'تركيب للفنيين' : 'Tarkeeb Pro'}
                </h1>
                <p className="text-xs text-gray-600">
                  {language === 'ar' ? 'لوحة تحكم الفني' : 'Technician Dashboard'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              {getVerificationStatusBadge()}
              <button
                onClick={() => navigate('/technician/notifications')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors relative"
              >
                <Bell className="w-5 h-5" />
                {/* Notification badge */}
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>
              <button
                onClick={() => navigate('/technician/settings')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pb-20">
        {children}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-around">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path || 
                           (item.id === 'dashboard' && location.pathname === '/technician');
            
            return (
              <button
                key={item.id}
                onClick={() => navigate(item.path)}
                className={`flex flex-col items-center space-y-1 py-2 px-3 rounded-lg transition-colors ${
                  isActive
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="text-xs font-medium">
                  {item.label[language]}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Verification Status Banner */}
      {profile?.verification_status === 'rejected' && (
        <div className="fixed top-16 left-4 right-4 bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm z-50">
          <div className="flex items-start space-x-3 space-x-reverse">
            <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="font-medium text-red-900">
                {language === 'ar' ? 'تم رفض طلبك' : 'Application Rejected'}
              </h4>
              <p className="text-red-700 text-sm mt-1">
                {profile.rejection_reason || (
                  language === 'ar' 
                    ? 'يرجى مراجعة المعلومات المقدمة وإعادة التقديم'
                    : 'Please review your information and reapply'
                )}
              </p>
              <button
                onClick={() => navigate('/technician/reapply')}
                className="text-red-600 hover:text-red-700 font-medium text-sm mt-2"
              >
                {language === 'ar' ? 'إعادة التقديم' : 'Reapply'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Pending Verification Banner */}
      {(profile?.verification_status === 'pending' || profile?.verification_status === 'under_review') && (
        <div className="fixed top-16 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm z-50">
          <div className="flex items-start space-x-3 space-x-reverse">
            <Clock className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900">
                {language === 'ar' ? 'طلبك قيد المراجعة' : 'Application Under Review'}
              </h4>
              <p className="text-blue-700 text-sm mt-1">
                {language === 'ar' 
                  ? 'سيتم مراجعة طلبك خلال 24-48 ساعة. ستتلقى إشعاراً عند الموافقة.'
                  : 'Your application will be reviewed within 24-48 hours. You will be notified once approved.'
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TechnicianLayout;
