import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AppProvider, useApp } from './contexts/AppContext';
import HomePage from './pages/HomePage';
import AuthPage from './pages/AuthPage';
import AuthCallback from './pages/AuthCallback';
import BookingPage from './pages/BookingPage';
import BookingsPage from './pages/BookingsPage';
import TechnicianDashboard from './pages/TechnicianDashboard';
import TechnicianDashboardNew from './pages/TechnicianDashboardNew';
import BookingConfirmation from './pages/BookingConfirmation';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import TechnicianAuthPage from './pages/TechnicianAuthPage';
import TechnicianOnboarding from './pages/TechnicianOnboarding';

const AppRoutes: React.FC = () => {
  const { user, profile, loading } = useApp();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل... / Loading...</p>
        </div>
      </div>
    );
  }

  // Show auth page if user exists but no profile or no role set
  if (user && (!profile || !profile.role)) {
    return (
      <Router>
        <Routes>
          <Route path="/technician/auth" element={<TechnicianAuthPage />} />
          <Route path="*" element={<AuthPage />} />
        </Routes>
      </Router>
    );
  }

  // Handle technician onboarding flow
  if (user && profile?.role === 'technician' && !profile.onboarding_completed) {
    return (
      <Router>
        <Routes>
          <Route path="/technician/onboarding" element={<TechnicianOnboarding />} />
          <Route path="*" element={<Navigate to="/technician/onboarding" />} />
        </Routes>
      </Router>
    );
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/auth/callback" element={<AuthCallback />} />
        <Route path="/technician/auth" element={<TechnicianAuthPage />} />
        <Route path="/technician/onboarding" element={<TechnicianOnboarding />} />
        
        {/* Protected routes */}
        <Route 
          path="/booking/:serviceId" 
          element={user && profile ? <BookingPage /> : <Navigate to="/auth" />} 
        />
        <Route 
          path="/bookings" 
          element={user && profile ? <BookingsPage /> : <Navigate to="/auth" />} 
        />
        <Route 
          path="/confirmation/:bookingId" 
          element={user && profile ? <BookingConfirmation /> : <Navigate to="/auth" />} 
        />
        
        {/* Technician routes */}
        <Route
          path="/technician"
          element={
            user && profile?.role === 'technician' && profile.onboarding_completed
              ? <TechnicianDashboardNew />
              : <Navigate to="/technician/auth" />
          }
        />
        <Route
          path="/technician/dashboard"
          element={
            user && profile && profile.role === 'technician' && profile.onboarding_completed
              ? <TechnicianDashboardNew />
              : <Navigate to="/technician/auth" />
          }
        />
        
        {/* Profile and Settings routes */}
        <Route
          path="/profile"
          element={
            user && profile
              ? <ProfilePage />
              : <Navigate to="/auth" />
          }
        />
        <Route
          path="/settings"
          element={
            user && profile
              ? <SettingsPage />
              : <Navigate to="/auth" />
          }
        />
        
        {/* Catch all */}
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
    </Router>
  );
};

function App() {
  return (
    <AppProvider>
      <AppRoutes />
    </AppProvider>
  );
}

export default App;