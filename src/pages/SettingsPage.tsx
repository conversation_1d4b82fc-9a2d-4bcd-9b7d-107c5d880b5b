import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Globe, 
  Bell, 
  Shield, 
  HelpCircle, 
  Info, 
  ChevronRight, 
  Check,
  Moon,
  Sun,
  Volume2,
  VolumeX
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import Layout from '../components/Layout';

const SettingsPage: React.FC = () => {
  const { language, setLanguage, user, profile } = useApp();
  const navigate = useNavigate();
  
  const [notifications, setNotifications] = useState({
    bookingUpdates: true,
    promotions: false,
    reminders: true,
    sound: true
  });

  React.useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
  }, [user, navigate]);

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
  };

  const toggleNotification = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  if (!user || !profile) {
    return null;
  }

  const settingsSections = [
    {
      title: { ar: 'التفضيلات', en: 'Preferences' },
      items: [
        {
          icon: Globe,
          title: { ar: 'اللغة', en: 'Language' },
          subtitle: { ar: language === 'ar' ? 'العربية' : 'الإنجليزية', en: language === 'ar' ? 'Arabic' : 'English' },
          action: 'language'
        }
      ]
    },
    {
      title: { ar: 'الإشعارات', en: 'Notifications' },
      items: [
        {
          icon: Bell,
          title: { ar: 'تحديثات الحجز', en: 'Booking Updates' },
          subtitle: { ar: 'إشعارات حالة الحجز', en: 'Booking status notifications' },
          action: 'toggle',
          key: 'bookingUpdates',
          value: notifications.bookingUpdates
        },
        {
          icon: Bell,
          title: { ar: 'التذكيرات', en: 'Reminders' },
          subtitle: { ar: 'تذكيرات المواعيد', en: 'Appointment reminders' },
          action: 'toggle',
          key: 'reminders',
          value: notifications.reminders
        },
        {
          icon: Volume2,
          title: { ar: 'الصوت', en: 'Sound' },
          subtitle: { ar: 'أصوات الإشعارات', en: 'Notification sounds' },
          action: 'toggle',
          key: 'sound',
          value: notifications.sound
        },
        {
          icon: Bell,
          title: { ar: 'العروض الترويجية', en: 'Promotions' },
          subtitle: { ar: 'عروض وخصومات', en: 'Offers and discounts' },
          action: 'toggle',
          key: 'promotions',
          value: notifications.promotions
        }
      ]
    },
    {
      title: { ar: 'الحساب والأمان', en: 'Account & Security' },
      items: [
        {
          icon: Shield,
          title: { ar: 'الخصوصية والأمان', en: 'Privacy & Security' },
          subtitle: { ar: 'إدارة إعدادات الخصوصية', en: 'Manage privacy settings' },
          action: 'navigate',
          path: '/privacy'
        }
      ]
    },
    {
      title: { ar: 'المساعدة والدعم', en: 'Help & Support' },
      items: [
        {
          icon: HelpCircle,
          title: { ar: 'مركز المساعدة', en: 'Help Center' },
          subtitle: { ar: 'الأسئلة الشائعة والدعم', en: 'FAQs and support' },
          action: 'navigate',
          path: '/help'
        },
        {
          icon: Info,
          title: { ar: 'حول التطبيق', en: 'About App' },
          subtitle: { ar: 'معلومات التطبيق والإصدار', en: 'App info and version' },
          action: 'navigate',
          path: '/about'
        }
      ]
    }
  ];

  const handleItemPress = (item: any) => {
    switch (item.action) {
      case 'language':
        // Show language picker
        break;
      case 'toggle':
        if (item.key) {
          toggleNotification(item.key);
        }
        break;
      case 'navigate':
        if (item.path) {
          navigate(item.path);
        }
        break;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-4 text-white">
          <h1 className="text-xl font-bold text-center">
            {language === 'ar' ? 'الإعدادات' : 'Settings'}
          </h1>
        </div>

        {/* Language Selection Modal */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-4">
              {language === 'ar' ? 'اختر اللغة' : 'Choose Language'}
            </h3>
            
            <div className="space-y-2">
              <button
                onClick={() => handleLanguageChange('ar')}
                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                  language === 'ar' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <span className="text-gray-900">العربية</span>
                {language === 'ar' && <Check className="w-5 h-5 text-blue-600" />}
              </button>
              
              <button
                onClick={() => handleLanguageChange('en')}
                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                  language === 'en' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <span className="text-gray-900">English</span>
                {language === 'en' && <Check className="w-5 h-5 text-blue-600" />}
              </button>
            </div>
          </div>
        </div>

        {/* Settings Sections */}
        {settingsSections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="px-4">
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <h3 className="font-semibold text-gray-900">
                  {section.title[language]}
                </h3>
              </div>
              
              <div className="divide-y divide-gray-100">
                {section.items.map((item, itemIndex) => {
                  const Icon = item.icon;
                  
                  return (
                    <button
                      key={itemIndex}
                      onClick={() => handleItemPress(item)}
                      className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <Icon className="w-5 h-5 text-gray-600" />
                        <div className="text-left">
                          <p className="font-medium text-gray-900">
                            {item.title[language]}
                          </p>
                          <p className="text-sm text-gray-600">
                            {item.subtitle[language]}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        {item.action === 'toggle' && (
                          <div className={`w-12 h-6 rounded-full transition-colors ${
                            item.value ? 'bg-blue-600' : 'bg-gray-300'
                          }`}>
                            <div className={`w-5 h-5 bg-white rounded-full shadow-sm transition-transform mt-0.5 ${
                              item.value ? 'translate-x-6' : 'translate-x-0.5'
                            }`} />
                          </div>
                        )}
                        {item.action !== 'toggle' && (
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        ))}

        {/* App Version */}
        <div className="px-4 pb-6">
          <div className="text-center text-gray-500 text-sm">
            <p>{language === 'ar' ? 'تركيب - الإصدار' : 'Tarkeeb - Version'} 1.0.0</p>
            <p className="mt-1">
              {language === 'ar' ? 'جميع الحقوق محفوظة © 2024' : 'All rights reserved © 2024'}
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SettingsPage;
