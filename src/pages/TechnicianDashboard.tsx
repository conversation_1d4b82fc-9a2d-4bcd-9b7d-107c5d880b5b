import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Phone, ToggleLeft, ToggleRight, Clock, Star } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { updateProfile, updateBooking } from '../lib/supabase';
import Layout from '../components/Layout';
import RatingStars from '../components/RatingStars';

const TechnicianDashboard: React.FC = () => {
  const { language, user, profile, bookings, refreshProfile, refreshBookings } = useApp();
  const [isAvailable, setIsAvailable] = useState(profile?.is_available || false);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (profile) {
      setIsAvailable(profile.is_available);
    }
  }, [profile]);

  useEffect(() => {
    refreshBookings();
  }, []);

  // Filter bookings for current technician
  const technicianBookings = bookings.filter(booking => 
    booking.technician_id === user?.id
  );

  const pendingJobs = technicianBookings.filter(booking => booking.status === 'pending');
  const upcomingJobs = technicianBookings.filter(booking => booking.status === 'confirmed');
  const inProgressJobs = technicianBookings.filter(booking => booking.status === 'in_progress');
  const completedJobs = technicianBookings.filter(booking => booking.status === 'completed');

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-EG');
  };

  const toggleAvailability = async () => {
    if (!user) return;
    
    setUpdating(true);
    try {
      const newAvailability = !isAvailable;
      const { error } = await updateProfile(user.id, { is_available: newAvailability });
      if (error) throw error;
      
      setIsAvailable(newAvailability);
      await refreshProfile();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setUpdating(false);
    }
  };

  const handleBookingAction = async (bookingId: string, action: 'accept' | 'decline' | 'start' | 'complete') => {
    try {
      let updates: any = {};

      switch (action) {
        case 'accept':
          updates = { status: 'confirmed', technician_id: user?.id };
          break;
        case 'decline':
          // When declining, remove technician assignment but keep status as pending for other technicians
          updates = { technician_id: null, status: 'pending' };
          break;
        case 'start':
          updates = { status: 'in_progress' };
          break;
        case 'complete':
          updates = { status: 'completed' };
          break;
      }

      const { error } = await updateBooking(bookingId, updates);
      if (error) throw error;

      await refreshBookings();
    } catch (error: any) {
      alert(error.message);
    }
  };

  if (!user || !profile || profile.role !== 'technician') {
    return null;
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header Stats */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-4 text-white">
          <div className="text-center mb-4">
            <h1 className="text-xl font-bold">
              {language === 'ar' ? 'لوحة التحكم' : 'Dashboard'}
            </h1>
            <p className="text-blue-100">
              {language === 'ar' ? `أهلاً ${profile.name}` : `Welcome ${profile.name}`}
            </p>
          </div>
          
          <div className="grid grid-cols-4 gap-3 text-center">
            <div>
              <div className="text-xl font-bold">{pendingJobs.length}</div>
              <div className="text-xs text-blue-100">
                {language === 'ar' ? 'جديد' : 'Pending'}
              </div>
            </div>
            <div>
              <div className="text-xl font-bold">{upcomingJobs.length}</div>
              <div className="text-xs text-blue-100">
                {language === 'ar' ? 'مؤكد' : 'Confirmed'}
              </div>
            </div>
            <div>
              <div className="text-xl font-bold">{inProgressJobs.length}</div>
              <div className="text-xs text-blue-100">
                {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
              </div>
            </div>
            <div>
              <div className="text-xl font-bold">{completedJobs.length}</div>
              <div className="text-xs text-blue-100">
                {language === 'ar' ? 'مكتمل' : 'Completed'}
              </div>
            </div>
          </div>
        </div>

        {/* Availability Toggle */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900">
                  {language === 'ar' ? 'الحالة' : 'Availability'}
                </h3>
                <p className="text-sm text-gray-600">
                  {isAvailable 
                    ? (language === 'ar' ? 'متاح لتلقي طلبات جديدة' : 'Available for new requests')
                    : (language === 'ar' ? 'غير متاح حالياً' : 'Currently unavailable')
                  }
                </p>
              </div>
              <button
                onClick={toggleAvailability}
                disabled={updating}
                className={`p-1 rounded-full transition-colors ${
                  isAvailable ? 'text-green-600' : 'text-gray-400'
                } ${updating ? 'opacity-50' : ''}`}
              >
                {isAvailable ? (
                  <ToggleRight className="w-8 h-8" />
                ) : (
                  <ToggleLeft className="w-8 h-8" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Pending Jobs */}
        <div className="px-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {language === 'ar' ? 'طلبات جديدة' : 'Pending Requests'}
          </h2>
          <div className="space-y-3">
            {pendingJobs.length > 0 ? (
              pendingJobs.map((booking) => (
                <div key={booking.id} className="bg-white rounded-xl p-4 shadow-sm border border-orange-200">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">
                        {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {booking.address}
                      </p>
                      {booking.customer && (
                        <p className="text-sm text-gray-600 mt-1">
                          {language === 'ar' ? 'العميل:' : 'Customer:'} {booking.customer.name}
                        </p>
                      )}
                    </div>
                    <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-lg text-xs font-medium">
                      {language === 'ar' ? 'جديد' : 'New'}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-3">
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(booking.scheduled_date)}</span>
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Clock className="w-4 h-4" />
                      <span>{booking.scheduled_time}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-blue-600">
                      {booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}
                    </span>
                    <div className="flex space-x-2 space-x-reverse">
                      <button 
                        onClick={() => handleBookingAction(booking.id, 'decline')}
                        className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors"
                      >
                        {language === 'ar' ? 'رفض' : 'Decline'}
                      </button>
                      <button 
                        onClick={() => handleBookingAction(booking.id, 'accept')}
                        className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                      >
                        {language === 'ar' ? 'قبول' : 'Accept'}
                      </button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                {language === 'ar' ? 'لا توجد طلبات جديدة' : 'No pending requests'}
              </div>
            )}
          </div>
        </div>

        {/* Upcoming Jobs */}
        <div className="px-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {language === 'ar' ? 'المواعيد القادمة' : 'Upcoming Jobs'}
          </h2>
          <div className="space-y-3">
            {upcomingJobs.map((booking) => (
              <div key={booking.id} className="bg-white rounded-xl p-4 shadow-sm border border-green-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {booking.address}
                    </p>
                    {booking.customer && (
                      <p className="text-sm text-gray-600 mt-1">
                        {language === 'ar' ? 'العميل:' : 'Customer:'} {booking.customer.name}
                      </p>
                    )}
                  </div>
                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded-lg text-xs font-medium">
                    {language === 'ar' ? 'مؤكد' : 'Confirmed'}
                  </span>
                </div>
                
                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-3">
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(booking.scheduled_date)}</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Clock className="w-4 h-4" />
                    <span>{booking.scheduled_time}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-blue-600">
                    {booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}
                  </span>
                  <div className="flex space-x-2 space-x-reverse">
                    {booking.customer?.phone && (
                      <a
                        href={`tel:${booking.customer.phone}`}
                        className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors flex items-center space-x-1 space-x-reverse"
                      >
                        <Phone className="w-4 h-4" />
                        <span>{language === 'ar' ? 'اتصال' : 'Call'}</span>
                      </a>
                    )}
                    <button 
                      onClick={() => handleBookingAction(booking.id, 'start')}
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                    >
                      {language === 'ar' ? 'بدء الخدمة' : 'Start Service'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* In Progress Jobs */}
        <div className="px-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {language === 'ar' ? 'الخدمات قيد التنفيذ' : 'In Progress Jobs'}
          </h2>
          <div className="space-y-3">
            {inProgressJobs.map((booking) => (
              <div key={booking.id} className="bg-white rounded-xl p-4 shadow-sm border border-blue-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">
                      {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {booking.address}
                    </p>
                    {booking.customer && (
                      <p className="text-sm text-gray-600 mt-1">
                        {language === 'ar' ? 'العميل:' : 'Customer:'} {booking.customer.name}
                      </p>
                    )}
                  </div>
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-lg text-xs font-medium">
                    {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
                  </span>
                </div>

                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-3">
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(booking.scheduled_date)}</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Clock className="w-4 h-4" />
                    <span>{booking.scheduled_time}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-semibold text-blue-600">
                    {booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}
                  </span>
                  <div className="flex space-x-2 space-x-reverse">
                    {booking.customer?.phone && (
                      <a
                        href={`tel:${booking.customer.phone}`}
                        className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors flex items-center space-x-1 space-x-reverse"
                      >
                        <Phone className="w-4 h-4" />
                        <span>{language === 'ar' ? 'اتصال' : 'Call'}</span>
                      </a>
                    )}
                    <button
                      onClick={() => handleBookingAction(booking.id, 'complete')}
                      className="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors"
                    >
                      {language === 'ar' ? 'إنهاء الخدمة' : 'Complete Service'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
            {inProgressJobs.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {language === 'ar' ? 'لا توجد خدمات قيد التنفيذ' : 'No jobs in progress'}
              </div>
            )}
          </div>
        </div>

        {/* Profile Summary */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-3">
              {language === 'ar' ? 'ملف التقييم' : 'Rating Profile'}
            </h3>
            <div className="flex items-center space-x-3 space-x-reverse">
              <img
                src={profile.avatar_url || 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=80&h=80&fit=crop'}
                alt="Profile"
                className="w-16 h-16 rounded-full object-cover"
              />
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{profile.name}</h4>
                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                  <RatingStars rating={profile.rating} size="sm" />
                  <span className="text-sm text-gray-600">{profile.rating}</span>
                </div>
                <p className="text-sm text-gray-600">
                  {completedJobs.length} {language === 'ar' ? 'خدمة مكتملة' : 'completed services'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TechnicianDashboard;