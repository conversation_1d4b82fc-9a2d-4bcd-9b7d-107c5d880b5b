import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, Lock, User, ChevronLeft, ChevronRight } from 'lucide-react';
import { Auth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';
import { useApp } from '../contexts/AppContext';
import { supabase, signInWithEmail, signUpWithEmail, updateProfile } from '../lib/supabase';
import Layout from '../components/Layout';

const AuthPage: React.FC = () => {
  const { language, isRTL, user, profile } = useApp();
  const [mode, setMode] = useState<'signin' | 'signup' | 'role'>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState<'customer' | 'technician'>('customer');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const ChevronIcon = isRTL ? ChevronRight : ChevronLeft;

  // If user is logged in but no profile role is set, show role selection
  React.useEffect(() => {
    if (user && profile?.role) {
      navigate(profile.role === 'technician' ? '/technician' : '/');
    } else if (user && profile && !profile.role) {
      setMode('role');
    }
  }, [user, profile, navigate]);

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (mode === 'signup') {
        const { error } = await signUpWithEmail(email, password, name);
        if (error) throw error;
        // For development, we'll auto-confirm. In production, you'd need email confirmation
        alert(language === 'ar' ? 'تم إنشاء الحساب بنجاح' : 'Account created successfully');
      } else {
        const { error } = await signInWithEmail(email, password);
        if (error) throw error;
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleSubmit = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const { error } = await updateProfile(user.id, { role });
      if (error) throw error;
      navigate(role === 'technician' ? '/technician' : '/');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (mode === 'role') {
    return (
      <Layout showNav={false}>
        <div className="min-h-screen flex flex-col">
          <div className="flex items-center p-4">
            <button 
              onClick={() => navigate('/')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronIcon className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="flex-1 text-center text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'اختر نوع الحساب' : 'Choose Account Type'}
            </h1>
            <div className="w-9" />
          </div>

          <div className="flex-1 px-4 py-8">
            <div className="max-w-sm mx-auto space-y-6">
              <div className="text-center space-y-2">
                <h2 className="text-xl font-bold text-gray-900">
                  {language === 'ar' ? 'كيف تريد استخدام تركيب؟' : 'How do you want to use Tarkeeb?'}
                </h2>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => setRole('customer')}
                  className={`w-full p-4 rounded-xl border-2 transition-all ${
                    role === 'customer'
                      ? 'border-blue-600 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-right">
                    <h3 className="font-semibold text-gray-900">
                      {language === 'ar' ? 'عميل' : 'Customer'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === 'ar' 
                        ? 'أريد حجز خدمة منزلية'
                        : 'I want to book home services'
                      }
                    </p>
                  </div>
                </button>

                <button
                  onClick={() => setRole('technician')}
                  className={`w-full p-4 rounded-xl border-2 transition-all ${
                    role === 'technician'
                      ? 'border-blue-600 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-right">
                    <h3 className="font-semibold text-gray-900">
                      {language === 'ar' ? 'فني' : 'Technician'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {language === 'ar' 
                        ? 'أريد تقديم خدمات منزلية'
                        : 'I want to provide home services'
                      }
                    </p>
                  </div>
                </button>
              </div>

              <button
                onClick={handleRoleSubmit}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-semibold py-3 rounded-xl transition-colors"
              >
                {loading 
                  ? (language === 'ar' ? 'جاري الحفظ...' : 'Saving...')
                  : (language === 'ar' ? 'متابعة' : 'Continue')
                }
              </button>

              {error && (
                <div className="text-red-600 text-sm text-center">{error}</div>
              )}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={false}>
      <div className="min-h-screen flex flex-col">
        <div className="flex items-center p-4">
          <button 
            onClick={() => navigate('/')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronIcon className="w-5 h-5 text-gray-600" />
          </button>
          <h1 className="flex-1 text-center text-lg font-semibold text-gray-900">
            {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
          </h1>
          <div className="w-9" />
        </div>

        <div className="flex-1 px-4 py-8">
          <div className="max-w-sm mx-auto space-y-6">
            {/* Toggle between signin/signup */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setMode('signin')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  mode === 'signin'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {language === 'ar' ? 'تسجيل دخول' : 'Sign In'}
              </button>
              <button
                onClick={() => setMode('signup')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  mode === 'signup'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {language === 'ar' ? 'إنشاء حساب' : 'Sign Up'}
              </button>
            </div>

            {/* Google Auth */}
            <div className="space-y-4">
              <Auth
                supabaseClient={supabase}
                appearance={{
                  theme: ThemeSupa,
                  variables: {
                    default: {
                      colors: {
                        brand: '#2E86DE',
                        brandAccent: '#1d4ed8',
                      },
                    },
                  },
                }}
                providers={['google']}
                onlyThirdPartyProviders
                redirectTo={`${window.location.origin}/auth/callback`}
                localization={{
                  variables: {
                    sign_in: {
                      social_provider_text: language === 'ar' 
                        ? 'تسجيل الدخول باستخدام {{provider}}'
                        : 'Sign in with {{provider}}',
                    },
                  },
                }}
              />

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-gray-50 text-gray-500">
                    {language === 'ar' ? 'أو' : 'or'}
                  </span>
                </div>
              </div>
            </div>

            {/* Email/Password Form */}
            <form onSubmit={handleEmailAuth} className="space-y-4">
              {mode === 'signup' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'ar' ? 'الاسم' : 'Name'}
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder={language === 'ar' ? 'أدخل اسمك' : 'Enter your name'}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'ar' ? 'كلمة المرور' : 'Password'}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter your password'}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    minLength={6}
                  />
                </div>
              </div>

              {error && (
                <div className="text-red-600 text-sm text-center">{error}</div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-semibold py-3 rounded-xl transition-colors"
              >
                {loading 
                  ? (language === 'ar' ? 'جاري التحميل...' : 'Loading...')
                  : mode === 'signup'
                    ? (language === 'ar' ? 'إنشاء حساب' : 'Create Account')
                    : (language === 'ar' ? 'تسجيل دخول' : 'Sign In')
                }
              </button>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AuthPage;