import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Camera, Edit2, Save, X, Phone, Mail, User, Star, Calendar, LogOut } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { updateProfile, signOut } from '../lib/supabase';
import Layout from '../components/Layout';
import RatingStars from '../components/RatingStars';

const ProfilePage: React.FC = () => {
  const { language, user, profile, refreshProfile, bookings } = useApp();
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Form state
  const [formData, setFormData] = useState({
    name: profile?.name || '',
    phone: profile?.phone || '',
    avatar_url: profile?.avatar_url || ''
  });

  React.useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
    if (profile) {
      setFormData({
        name: profile.name,
        phone: profile.phone || '',
        avatar_url: profile.avatar_url || ''
      });
    }
  }, [user, profile, navigate]);

  const handleSave = async () => {
    if (!user) return;
    
    setLoading(true);
    setError('');
    
    try {
      const { error } = await updateProfile(user.id, {
        name: formData.name,
        phone: formData.phone,
        avatar_url: formData.avatar_url
      });
      
      if (error) throw error;
      
      await refreshProfile();
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        name: profile.name,
        phone: profile.phone || '',
        avatar_url: profile.avatar_url || ''
      });
    }
    setIsEditing(false);
    setError('');
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleAvatarClick = () => {
    if (isEditing && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // For now, we'll use a placeholder URL
      // In a real app, you'd upload to Supabase storage
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData(prev => ({
          ...prev,
          avatar_url: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  if (!user || !profile) {
    return null;
  }

  const completedBookings = bookings.filter(booking => booking.status === 'completed');
  const totalSpent = completedBookings.reduce((sum, booking) => sum + booking.total_price, 0);

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-4 text-white">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">
              {language === 'ar' ? 'الملف الشخصي' : 'Profile'}
            </h1>
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
              >
                <Edit2 className="w-5 h-5" />
              </button>
            ) : (
              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={handleCancel}
                  className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Save className="w-5 h-5" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Profile Info */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex flex-col items-center space-y-4">
              {/* Avatar */}
              <div className="relative">
                <div 
                  className={`w-24 h-24 rounded-full overflow-hidden ${isEditing ? 'cursor-pointer' : ''}`}
                  onClick={handleAvatarClick}
                >
                  <img
                    src={formData.avatar_url || 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop'}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                {isEditing && (
                  <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                    <Camera className="w-6 h-6 text-white" />
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>

              {/* Name */}
              {isEditing ? (
                <div className="w-full">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'ar' ? 'الاسم' : 'Name'}
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              ) : (
                <h2 className="text-xl font-bold text-gray-900">{profile.name}</h2>
              )}

              {/* Role Badge */}
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                profile.role === 'technician' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-green-100 text-green-700'
              }`}>
                {profile.role === 'technician' 
                  ? (language === 'ar' ? 'فني' : 'Technician')
                  : (language === 'ar' ? 'عميل' : 'Customer')
                }
              </span>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-4">
              {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
            </h3>

            <div className="space-y-4">
              {/* Email */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="w-5 h-5 text-gray-400" />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  </p>
                  <p className="text-gray-900">{user.email}</p>
                </div>
              </div>

              {/* Phone */}
              <div className="flex items-center space-x-3 space-x-reverse">
                <Phone className="w-5 h-5 text-gray-400" />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">
                    {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                  </p>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder={language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900">{profile.phone || (language === 'ar' ? 'غير محدد' : 'Not provided')}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-4">
              {language === 'ar' ? 'الإحصائيات' : 'Statistics'}
            </h3>

            <div className="grid grid-cols-2 gap-4">
              {profile.role === 'customer' ? (
                <>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{completedBookings.length}</div>
                    <div className="text-sm text-blue-700">
                      {language === 'ar' ? 'خدمة مكتملة' : 'Services Completed'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{totalSpent}</div>
                    <div className="text-sm text-green-700">
                      {language === 'ar' ? 'إجمالي الإنفاق (ج.م)' : 'Total Spent (EGP)'}
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-center space-x-1 space-x-reverse">
                      <RatingStars rating={profile.rating} size="sm" />
                      <span className="text-2xl font-bold text-blue-600">{profile.rating}</span>
                    </div>
                    <div className="text-sm text-blue-700">
                      {language === 'ar' ? 'التقييم' : 'Rating'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{completedBookings.length}</div>
                    <div className="text-sm text-green-700">
                      {language === 'ar' ? 'خدمة مكتملة' : 'Jobs Completed'}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Account Actions */}
        <div className="px-4">
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-4">
              {language === 'ar' ? 'إعدادات الحساب' : 'Account Settings'}
            </h3>

            <div className="space-y-3">
              <button
                onClick={() => navigate('/settings')}
                className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  <User className="w-5 h-5 text-gray-600" />
                  <span className="text-gray-900">
                    {language === 'ar' ? 'الإعدادات' : 'Settings'}
                  </span>
                </div>
                <span className="text-gray-400">›</span>
              </button>

              <button
                onClick={handleSignOut}
                className="w-full flex items-center justify-between p-3 bg-red-50 hover:bg-red-100 rounded-lg transition-colors text-red-600"
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  <LogOut className="w-5 h-5" />
                  <span>{language === 'ar' ? 'تسجيل الخروج' : 'Sign Out'}</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="px-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProfilePage;
