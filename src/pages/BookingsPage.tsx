import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, MapPin, Star, Phone } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import Layout from '../components/Layout';

const BookingsPage: React.FC = () => {
  const { language, user, profile, bookings, refreshBookings } = useApp();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
    refreshBookings();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-700';
      case 'confirmed':
        return 'bg-green-100 text-green-700';
      case 'in_progress':
        return 'bg-blue-100 text-blue-700';
      case 'completed':
        return 'bg-gray-100 text-gray-700';
      case 'cancelled':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      pending: { ar: 'في الانتظار', en: 'Pending' },
      confirmed: { ar: 'مؤكد', en: 'Confirmed' },
      in_progress: { ar: 'قيد التنفيذ', en: 'In Progress' },
      completed: { ar: 'مكتمل', en: 'Completed' },
      cancelled: { ar: 'ملغي', en: 'Cancelled' },
    };
    return statusMap[status as keyof typeof statusMap]?.[language] || status;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-EG');
  };

  if (!user || !profile) {
    return null;
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-4 text-white">
          <h1 className="text-xl font-bold text-center">
            {language === 'ar' ? 'حجوزاتي' : 'My Bookings'}
          </h1>
        </div>

        {/* Bookings List */}
        <div className="px-4">
          {bookings.length > 0 ? (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">
                        {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {booking.address}
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusText(booking.status)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 mb-3">
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(booking.scheduled_date)}</span>
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Clock className="w-4 h-4" />
                      <span>{booking.scheduled_time}</span>
                    </div>
                  </div>

                  {/* Technician info for customers */}
                  {profile.role === 'customer' && booking.technician && (
                    <div className="flex items-center space-x-3 space-x-reverse mb-3 p-2 bg-gray-50 rounded-lg">
                      <img
                        src={booking.technician.avatar_url || 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&fit=crop'}
                        alt={booking.technician.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{booking.technician.name}</p>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-600">{booking.technician.rating}</span>
                        </div>
                      </div>
                      {booking.technician.phone && (
                        <a
                          href={`tel:${booking.technician.phone}`}
                          className="p-1 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
                        >
                          <Phone className="w-3 h-3" />
                        </a>
                      )}
                    </div>
                  )}

                  {/* Customer info for technicians */}
                  {profile.role === 'technician' && booking.customer && (
                    <div className="flex items-center space-x-3 space-x-reverse mb-3 p-2 bg-gray-50 rounded-lg">
                      <img
                        src={booking.customer.avatar_url || 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&fit=crop'}
                        alt={booking.customer.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{booking.customer.name}</p>
                        <p className="text-xs text-gray-600">
                          {language === 'ar' ? 'العميل' : 'Customer'}
                        </p>
                      </div>
                      {booking.customer.phone && (
                        <a
                          href={`tel:${booking.customer.phone}`}
                          className="p-1 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
                        >
                          <Phone className="w-3 h-3" />
                        </a>
                      )}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-blue-600">
                      {booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}
                    </span>
                    <button
                      onClick={() => navigate(`/confirmation/${booking.id}`)}
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                    >
                      {language === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {language === 'ar' ? 'لا توجد حجوزات' : 'No bookings yet'}
              </h3>
              <p className="text-gray-600 mb-6">
                {language === 'ar' 
                  ? 'ابدأ بحجز خدمتك الأولى'
                  : 'Start by booking your first service'
                }
              </p>
              <button
                onClick={() => navigate('/')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
              >
                {language === 'ar' ? 'تصفح الخدمات' : 'Browse Services'}
              </button>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default BookingsPage;