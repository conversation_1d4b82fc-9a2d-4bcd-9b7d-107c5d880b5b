import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  Clock, 
  DollarSign, 
  Star, 
  TrendingUp,
  MapPin,
  Phone,
  CheckCircle,
  XCircle,
  Play,
  Users,
  Briefcase,
  Award
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { updateBooking, updateProfile } from '../lib/supabase';
import TechnicianLayout from '../components/TechnicianLayout';
import RatingStars from '../components/RatingStars';

const TechnicianDashboardNew: React.FC = () => {
  const { language, user, profile, bookings, refreshBookings } = useApp();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    if (!user) {
      navigate('/technician/auth');
      return;
    }
    
    if (profile?.role !== 'technician') {
      navigate('/');
      return;
    }
  }, [user, profile, navigate]);

  const technicianBookings = bookings.filter(booking => booking.technician_id === user?.id);
  const pendingJobs = technicianBookings.filter(booking => booking.status === 'pending');
  const confirmedJobs = technicianBookings.filter(booking => booking.status === 'confirmed');
  const inProgressJobs = technicianBookings.filter(booking => booking.status === 'in_progress');
  const completedJobs = technicianBookings.filter(booking => booking.status === 'completed');

  // Calculate earnings
  const totalEarnings = completedJobs.reduce((sum, booking) => sum + booking.total_price, 0);
  const thisMonthEarnings = completedJobs
    .filter(booking => {
      const bookingDate = new Date(booking.created_at);
      const now = new Date();
      return bookingDate.getMonth() === now.getMonth() && bookingDate.getFullYear() === now.getFullYear();
    })
    .reduce((sum, booking) => sum + booking.total_price, 0);

  const toggleAvailability = async () => {
    if (!user || !profile) return;
    
    setLoading(true);
    try {
      const { error } = await updateProfile(user.id, {
        is_available: !profile.is_available
      });
      if (error) throw error;
      
      // Refresh profile data
      window.location.reload();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBookingAction = async (bookingId: string, action: 'accept' | 'decline' | 'start' | 'complete') => {
    try {
      let updates: any = {};
      
      switch (action) {
        case 'accept':
          updates = { status: 'confirmed', technician_id: user?.id };
          break;
        case 'decline':
          updates = { technician_id: null, status: 'pending' };
          break;
        case 'start':
          updates = { status: 'in_progress' };
          break;
        case 'complete':
          updates = { status: 'completed' };
          break;
      }
      
      const { error } = await updateBooking(bookingId, updates);
      if (error) throw error;
      
      await refreshBookings();
    } catch (error: any) {
      alert(error.message);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US');
  };

  if (!user || !profile) {
    return null;
  }

  const stats = [
    {
      icon: Briefcase,
      label: { ar: 'الطلبات المعلقة', en: 'Pending Jobs' },
      value: pendingJobs.length,
      color: 'bg-yellow-500'
    },
    {
      icon: Clock,
      label: { ar: 'قيد التنفيذ', en: 'In Progress' },
      value: inProgressJobs.length,
      color: 'bg-blue-500'
    },
    {
      icon: CheckCircle,
      label: { ar: 'مكتملة', en: 'Completed' },
      value: completedJobs.length,
      color: 'bg-green-500'
    },
    {
      icon: DollarSign,
      label: { ar: 'الأرباح الشهرية', en: 'Monthly Earnings' },
      value: `${thisMonthEarnings} ${language === 'ar' ? 'ج.م' : 'EGP'}`,
      color: 'bg-purple-500'
    }
  ];

  return (
    <TechnicianLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl font-bold">
                {language === 'ar' ? `مرحباً ${profile.name}` : `Welcome ${profile.name}`}
              </h1>
              <p className="text-blue-100 text-sm">
                {language === 'ar' ? 'لوحة تحكم الفني المحترف' : 'Professional Technician Dashboard'}
              </p>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <RatingStars rating={profile.rating} size="sm" />
              <span className="text-white font-semibold">{profile.rating}</span>
            </div>
          </div>

          {/* Availability Toggle */}
          <div className="flex items-center justify-between bg-white/10 rounded-lg p-3">
            <div>
              <h3 className="font-medium">
                {language === 'ar' ? 'حالة التوفر' : 'Availability Status'}
              </h3>
              <p className="text-blue-100 text-sm">
                {profile.is_available 
                  ? (language === 'ar' ? 'متاح لاستقبال طلبات جديدة' : 'Available for new requests')
                  : (language === 'ar' ? 'غير متاح حالياً' : 'Currently unavailable')
                }
              </p>
            </div>
            <button
              onClick={toggleAvailability}
              disabled={loading}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                profile.is_available ? 'bg-green-500' : 'bg-gray-400'
              } disabled:opacity-50`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  profile.is_available ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="px-4">
          <div className="grid grid-cols-2 gap-4">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="bg-white rounded-xl p-4 shadow-sm">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className={`w-10 h-10 ${stat.color} rounded-lg flex items-center justify-center`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <p className="text-sm text-gray-600">{stat.label[language]}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="px-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </h2>
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => navigate('/technician/jobs')}
              className="bg-white p-4 rounded-xl shadow-sm border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <Calendar className="w-6 h-6 text-blue-600 mb-2" />
              <p className="font-medium text-gray-900">
                {language === 'ar' ? 'عرض الطلبات' : 'View Jobs'}
              </p>
              <p className="text-sm text-gray-600">
                {pendingJobs.length} {language === 'ar' ? 'طلب جديد' : 'new requests'}
              </p>
            </button>
            
            <button
              onClick={() => navigate('/technician/analytics')}
              className="bg-white p-4 rounded-xl shadow-sm border border-gray-200 hover:border-green-300 transition-colors"
            >
              <TrendingUp className="w-6 h-6 text-green-600 mb-2" />
              <p className="font-medium text-gray-900">
                {language === 'ar' ? 'التقارير' : 'Analytics'}
              </p>
              <p className="text-sm text-gray-600">
                {language === 'ar' ? 'أداء هذا الشهر' : 'This month performance'}
              </p>
            </button>
          </div>
        </div>

        {/* Recent Jobs */}
        <div className="px-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'الطلبات الأخيرة' : 'Recent Jobs'}
            </h2>
            <button
              onClick={() => navigate('/technician/jobs')}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              {language === 'ar' ? 'عرض الكل' : 'View All'}
            </button>
          </div>

          <div className="space-y-3">
            {[...pendingJobs, ...inProgressJobs, ...confirmedJobs].slice(0, 3).map((booking) => (
              <div key={booking.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                    </h3>
                    <p className="text-sm text-gray-600 flex items-center mt-1">
                      <MapPin className="w-4 h-4 mr-1" />
                      {booking.address}
                    </p>
                    <p className="text-sm text-gray-600 flex items-center mt-1">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(booking.scheduled_date)} - {booking.scheduled_time}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-lg text-xs font-medium ${
                    booking.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                    booking.status === 'confirmed' ? 'bg-blue-100 text-blue-700' :
                    booking.status === 'in_progress' ? 'bg-purple-100 text-purple-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {booking.status === 'pending' ? (language === 'ar' ? 'معلق' : 'Pending') :
                     booking.status === 'confirmed' ? (language === 'ar' ? 'مؤكد' : 'Confirmed') :
                     booking.status === 'in_progress' ? (language === 'ar' ? 'قيد التنفيذ' : 'In Progress') :
                     (language === 'ar' ? 'مكتمل' : 'Completed')
                    }
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-semibold text-blue-600">
                    {booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}
                  </span>
                  
                  <div className="flex space-x-2 space-x-reverse">
                    {booking.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleBookingAction(booking.id, 'decline')}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleBookingAction(booking.id, 'accept')}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    {booking.status === 'confirmed' && (
                      <button
                        onClick={() => handleBookingAction(booking.id, 'start')}
                        className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                      >
                        <Play className="w-4 h-4 inline mr-1" />
                        {language === 'ar' ? 'بدء' : 'Start'}
                      </button>
                    )}
                    {booking.status === 'in_progress' && (
                      <button
                        onClick={() => handleBookingAction(booking.id, 'complete')}
                        className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors"
                      >
                        <CheckCircle className="w-4 h-4 inline mr-1" />
                        {language === 'ar' ? 'إنهاء' : 'Complete'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {[...pendingJobs, ...inProgressJobs, ...confirmedJobs].length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Briefcase className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p>{language === 'ar' ? 'لا توجد طلبات حالياً' : 'No active jobs'}</p>
                <p className="text-sm mt-1">
                  {language === 'ar' ? 'تأكد من أن حالة التوفر مفعلة' : 'Make sure your availability is turned on'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </TechnicianLayout>
  );
};

export default TechnicianDashboardNew;
