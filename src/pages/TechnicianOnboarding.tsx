import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  User, 
  Wrench, 
  Upload, 
  Star, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Phone,
  MapPin,
  FileText,
  Camera
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { updateProfile } from '../lib/supabase';

interface OnboardingStep {
  id: number;
  title: { ar: string; en: string };
  description: { ar: string; en: string };
  icon: React.ComponentType<any>;
}

const TechnicianOnboarding: React.FC = () => {
  const { language, user, profile, refreshProfile } = useApp();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Form data for all steps
  const [formData, setFormData] = useState({
    // Step 1: Personal Info
    phone: '',
    address: '',
    city: '',
    national_id: '',
    
    // Step 2: Services (will be handled separately)
    selectedServices: [] as string[],
    
    // Step 3: Experience
    experience_years: 0,
    bio: '',
    
    // Step 4: Documents
    nationalIdFront: null as File | null,
    nationalIdBack: null as File | null,
    certificates: [] as File[]
  });

  useEffect(() => {
    if (!user) {
      navigate('/technician/auth');
      return;
    }
    
    if (profile?.role !== 'technician') {
      navigate('/');
      return;
    }
    
    // If onboarding is already completed, redirect to dashboard
    if (profile?.onboarding_completed) {
      navigate('/technician/dashboard');
      return;
    }
  }, [user, profile, navigate]);

  const steps: OnboardingStep[] = [
    {
      id: 1,
      title: { ar: 'المعلومات الشخصية', en: 'Personal Information' },
      description: { ar: 'أدخل معلوماتك الشخصية الأساسية', en: 'Enter your basic personal information' },
      icon: User
    },
    {
      id: 2,
      title: { ar: 'اختيار الخدمات', en: 'Select Services' },
      description: { ar: 'اختر الخدمات التي تقدمها', en: 'Choose the services you provide' },
      icon: Wrench
    },
    {
      id: 3,
      title: { ar: 'الخبرة والمهارات', en: 'Experience & Skills' },
      description: { ar: 'أخبرنا عن خبرتك ومهاراتك', en: 'Tell us about your experience and skills' },
      icon: Star
    },
    {
      id: 4,
      title: { ar: 'رفع المستندات', en: 'Upload Documents' },
      description: { ar: 'ارفع الهوية والشهادات المطلوبة', en: 'Upload required ID and certificates' },
      icon: Upload
    }
  ];

  const handleNext = async () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding
      await completeOnboarding();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Update profile with all collected data
      const { error } = await updateProfile(user!.id, {
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        national_id: formData.national_id,
        experience_years: formData.experience_years,
        bio: formData.bio,
        onboarding_completed: true,
        verification_status: 'under_review'
      });
      
      if (error) throw error;
      
      await refreshProfile();
      navigate('/technician/dashboard');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.phone && formData.address && formData.city && formData.national_id;
      case 2:
        return formData.selectedServices.length > 0;
      case 3:
        return formData.experience_years > 0 && formData.bio.length > 20;
      case 4:
        return formData.nationalIdFront && formData.nationalIdBack;
      default:
        return false;
    }
  };

  if (!user || !profile) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900">
            {language === 'ar' ? 'إعداد حساب الفني' : 'Technician Setup'}
          </h1>
          <div className="text-sm text-gray-600">
            {currentStep} / {steps.length}
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex items-center space-x-2 space-x-reverse">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep > step.id 
                    ? 'bg-green-600 text-white' 
                    : currentStep === step.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-1 rounded ${
                    currentStep > step.id ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="p-4">
        <div className="bg-white rounded-xl p-6 shadow-sm">
          {/* Step Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              {React.createElement(steps[currentStep - 1].icon, {
                className: "w-8 h-8 text-blue-600"
              })}
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {steps[currentStep - 1].title[language]}
            </h2>
            <p className="text-gray-600">
              {steps[currentStep - 1].description[language]}
            </p>
          </div>

          {/* Step 1: Personal Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Phone className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'العنوان' : 'Address'}
                </label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' ? 'أدخل عنوانك' : 'Enter your address'}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'المدينة' : 'City'}
                </label>
                <select
                  value={formData.city}
                  onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">{language === 'ar' ? 'اختر المدينة' : 'Select City'}</option>
                  <option value="cairo">{language === 'ar' ? 'القاهرة' : 'Cairo'}</option>
                  <option value="giza">{language === 'ar' ? 'الجيزة' : 'Giza'}</option>
                  <option value="alexandria">{language === 'ar' ? 'الإسكندرية' : 'Alexandria'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'رقم الهوية الوطنية' : 'National ID Number'}
                </label>
                <input
                  type="text"
                  value={formData.national_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, national_id: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' ? 'أدخل رقم الهوية' : 'Enter your national ID'}
                  required
                />
              </div>
            </div>
          )}

          {/* Step 2: Service Selection */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <p className="text-gray-600 text-center mb-6">
                {language === 'ar'
                  ? 'اختر الخدمات التي تقدمها للعملاء. يمكنك اختيار أكثر من خدمة.'
                  : 'Select the services you provide to customers. You can choose multiple services.'
                }
              </p>

              <div className="grid grid-cols-1 gap-3">
                {[
                  { id: 'plumbing', name_ar: 'سباكة', name_en: 'Plumbing', icon: '🔧' },
                  { id: 'electrical', name_ar: 'كهرباء', name_en: 'Electrical', icon: '⚡' },
                  { id: 'carpentry', name_ar: 'نجارة', name_en: 'Carpentry', icon: '🔨' },
                  { id: 'painting', name_ar: 'دهان', name_en: 'Painting', icon: '🎨' },
                  { id: 'cleaning', name_ar: 'تنظيف', name_en: 'Cleaning', icon: '🧽' },
                  { id: 'appliance_repair', name_ar: 'إصلاح الأجهزة', name_en: 'Appliance Repair', icon: '🔧' }
                ].map((service) => (
                  <label
                    key={service.id}
                    className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.selectedServices.includes(service.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.selectedServices.includes(service.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({
                            ...prev,
                            selectedServices: [...prev.selectedServices, service.id]
                          }));
                        } else {
                          setFormData(prev => ({
                            ...prev,
                            selectedServices: prev.selectedServices.filter(id => id !== service.id)
                          }));
                        }
                      }}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-2xl">{service.icon}</span>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {language === 'ar' ? service.name_ar : service.name_en}
                        </h3>
                      </div>
                    </div>
                    {formData.selectedServices.includes(service.id) && (
                      <CheckCircle className="w-5 h-5 text-blue-600 ml-auto" />
                    )}
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Step 3: Experience & Skills */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Star className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'سنوات الخبرة' : 'Years of Experience'}
                </label>
                <select
                  value={formData.experience_years}
                  onChange={(e) => setFormData(prev => ({ ...prev, experience_years: parseInt(e.target.value) }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value={0}>{language === 'ar' ? 'اختر سنوات الخبرة' : 'Select years of experience'}</option>
                  <option value={1}>{language === 'ar' ? 'أقل من سنة' : 'Less than 1 year'}</option>
                  <option value={2}>{language === 'ar' ? '1-2 سنة' : '1-2 years'}</option>
                  <option value={5}>{language === 'ar' ? '3-5 سنوات' : '3-5 years'}</option>
                  <option value={10}>{language === 'ar' ? '5-10 سنوات' : '5-10 years'}</option>
                  <option value={15}>{language === 'ar' ? 'أكثر من 10 سنوات' : 'More than 10 years'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'نبذة عن خبرتك ومهاراتك' : 'About Your Experience & Skills'}
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar'
                    ? 'اكتب نبذة مختصرة عن خبرتك ومهاراتك في المجال (على الأقل 20 حرف)'
                    : 'Write a brief description of your experience and skills (at least 20 characters)'
                  }
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  {formData.bio.length}/20 {language === 'ar' ? 'حرف كحد أدنى' : 'characters minimum'}
                </p>
              </div>
            </div>
          )}

          {/* Step 4: Document Upload */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Camera className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'صورة الهوية (الوجه الأمامي)' : 'National ID (Front Side)'}
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setFormData(prev => ({ ...prev, nationalIdFront: e.target.files?.[0] || null }))}
                    className="hidden"
                    id="id-front"
                  />
                  <label htmlFor="id-front" className="cursor-pointer">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">
                      {formData.nationalIdFront
                        ? formData.nationalIdFront.name
                        : (language === 'ar' ? 'اضغط لرفع صورة الهوية' : 'Click to upload ID photo')
                      }
                    </p>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Camera className="w-4 h-4 inline mr-2" />
                  {language === 'ar' ? 'صورة الهوية (الوجه الخلفي)' : 'National ID (Back Side)'}
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setFormData(prev => ({ ...prev, nationalIdBack: e.target.files?.[0] || null }))}
                    className="hidden"
                    id="id-back"
                  />
                  <label htmlFor="id-back" className="cursor-pointer">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">
                      {formData.nationalIdBack
                        ? formData.nationalIdBack.name
                        : (language === 'ar' ? 'اضغط لرفع صورة الهوية' : 'Click to upload ID photo')
                      }
                    </p>
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">
                  {language === 'ar' ? 'مراجعة الطلب' : 'Application Review'}
                </h4>
                <p className="text-blue-700 text-sm">
                  {language === 'ar'
                    ? 'سيتم مراجعة طلبك خلال 24-48 ساعة. ستتلقى إشعاراً عند الموافقة على حسابك.'
                    : 'Your application will be reviewed within 24-48 hours. You will receive a notification when your account is approved.'
                  }
                </p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between mt-8">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>{language === 'ar' ? 'السابق' : 'Previous'}</span>
            </button>

            <button
              onClick={handleNext}
              disabled={!isStepValid() || loading}
              className="flex items-center space-x-2 space-x-reverse px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span>
                {currentStep === steps.length 
                  ? (language === 'ar' ? 'إنهاء الإعداد' : 'Complete Setup')
                  : (language === 'ar' ? 'التالي' : 'Next')
                }
              </span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechnicianOnboarding;
