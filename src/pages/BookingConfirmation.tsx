import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { CheckCircle, Clock, Phone, MapPin, Star } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { supabase, createRating } from '../lib/supabase';
import Layout from '../components/Layout';
import RatingStars from '../components/RatingStars';

const BookingConfirmation: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const { language, user, refreshBookings } = useApp();
  const [booking, setBooking] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [showRating, setShowRating] = useState(false);
  const [submittingRating, setSubmittingRating] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
    loadBooking();
  }, [bookingId, user]);

  const loadBooking = async () => {
    if (!bookingId) return;
    
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          service:services(*),
          customer:profiles!customer_id(*),
          technician:profiles!technician_id(*)
        `)
        .eq('id', bookingId)
        .single();
      
      if (error) throw error;
      setBooking(data);
    } catch (error) {
      console.error('Error loading booking:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-700';
      case 'confirmed':
        return 'bg-green-100 text-green-700';
      case 'completed':
        return 'bg-blue-100 text-blue-700';
      case 'cancelled':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      pending: { ar: 'في انتظار التأكيد', en: 'Pending Confirmation' },
      confirmed: { ar: 'مؤكد', en: 'Confirmed' },
      in_progress: { ar: 'قيد التنفيذ', en: 'In Progress' },
      completed: { ar: 'مكتمل', en: 'Completed' },
      cancelled: { ar: 'ملغي', en: 'Cancelled' },
    };
    return statusMap[status as keyof typeof statusMap]?.[language] || status;
  };

  const handleRateService = async () => {
    if (!booking || rating === 0) return;
    
    setSubmittingRating(true);
    try {
      const { error } = await createRating({
        booking_id: booking.id,
        customer_id: booking.customer_id,
        technician_id: booking.technician_id,
        rating,
        comment: comment.trim() || undefined,
      });
      
      if (error) throw error;
      
      setShowRating(false);
      alert(language === 'ar' ? 'شكراً لتقييمك!' : 'Thank you for your rating!');
      await refreshBookings();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setSubmittingRating(false);
    }
  };

  if (loading) {
    return (
      <Layout showNav={false}>
        <div className="p-4 text-center">
          <div className="animate-pulse">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </div>
        </div>
      </Layout>
    );
  }

  if (!booking) {
    return (
      <Layout showNav={false}>
        <div className="p-4 text-center">
          <p className="text-gray-600">
            {language === 'ar' ? 'لم يتم العثور على الحجز' : 'Booking not found'}
          </p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={false}>
      <div className="min-h-screen bg-gray-50">
        <div className="p-4 space-y-6">
          {/* Success Header */}
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {language === 'ar' ? 'تم الحجز بنجاح!' : 'Booking Successful!'}
            </h1>
            <p className="text-gray-600">
              {language === 'ar' 
                ? 'سيتم التواصل معك قريباً لتأكيد الموعد'
                : 'You will be contacted soon to confirm the appointment'
              }
            </p>
          </div>

          {/* Booking Status */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {language === 'ar' ? 'حالة الحجز' : 'Booking Status'}
              </h2>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                {getStatusText(booking.status)}
              </span>
            </div>

            {/* Status Timeline */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {language === 'ar' ? 'تم إرسال الطلب' : 'Request Sent'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(booking.created_at).toLocaleString(language === 'ar' ? 'ar-EG' : 'en-EG')}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  ['confirmed', 'in_progress', 'completed'].includes(booking.status) ? 'bg-green-500' : 'bg-gray-300'
                }`}>
                  <Clock className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {language === 'ar' ? 'تأكيد الفني' : 'Technician Confirmation'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {['confirmed', 'in_progress', 'completed'].includes(booking.status)
                      ? (language === 'ar' ? 'تم التأكيد' : 'Confirmed')
                      : (language === 'ar' ? 'في الانتظار' : 'Pending')
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  booking.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                }`}>
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {language === 'ar' ? 'اكتمال الخدمة' : 'Service Completion'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {booking.status === 'completed' 
                      ? (language === 'ar' ? 'مكتمل' : 'Completed')
                      : (language === 'ar' ? 'لم يبدأ' : 'Not started')
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Service Details */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-3">
              {language === 'ar' ? 'تفاصيل الخدمة' : 'Service Details'}
            </h3>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-gray-900">
                  {language === 'ar' ? booking.service?.name_ar : booking.service?.name_en}
                </h4>
                <p className="text-sm text-gray-600">
                  {booking.scheduled_date} - {booking.scheduled_time}
                </p>
              </div>
              <div className="flex items-start space-x-2 space-x-reverse">
                <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                <p className="text-sm text-gray-600 flex-1">
                  {booking.address}
                </p>
              </div>
              {booking.notes && (
                <div>
                  <p className="text-sm font-medium text-gray-700">
                    {language === 'ar' ? 'ملاحظات:' : 'Notes:'}
                  </p>
                  <p className="text-sm text-gray-600">{booking.notes}</p>
                </div>
              )}
            </div>
          </div>

          {/* Technician Info */}
          {booking.technician && (
            <div className="bg-white rounded-xl p-4 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-3">
                {language === 'ar' ? 'الفني المكلف' : 'Assigned Technician'}
              </h3>
              <div className="flex items-center space-x-3 space-x-reverse">
                <img
                  src={booking.technician.avatar_url || 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'}
                  alt={booking.technician.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{booking.technician.name}</h4>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">{booking.technician.rating}</span>
                  </div>
                </div>
                {booking.technician.phone && (
                  <a
                    href={`tel:${booking.technician.phone}`}
                    className="p-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <Phone className="w-4 h-4" />
                  </a>
                )}
              </div>
            </div>
          )}

          {/* Payment Summary */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-3">
              {language === 'ar' ? 'ملخص الدفع' : 'Payment Summary'}
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">{language === 'ar' ? 'سعر الخدمة' : 'Service Price'}</span>
                <span className="font-medium">{booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{language === 'ar' ? 'طريقة الدفع' : 'Payment Method'}</span>
                <span className="text-sm text-gray-600">
                  {booking.payment_method === 'cash' 
                    ? (language === 'ar' ? 'نقدي عند الوصول' : 'Cash on arrival')
                    : booking.payment_method
                  }
                </span>
              </div>
              <hr className="my-2" />
              <div className="flex justify-between font-semibold text-lg">
                <span>{language === 'ar' ? 'الإجمالي' : 'Total'}</span>
                <span className="text-blue-600">{booking.total_price} {language === 'ar' ? 'ج.م' : 'EGP'}</span>
              </div>
            </div>
          </div>

          {/* Rating Section (only show if completed and customer) */}
          {booking.status === 'completed' && booking.customer_id === user?.id && (
            <div className="bg-white rounded-xl p-4 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-3">
                {language === 'ar' ? 'قيم الخدمة' : 'Rate Service'}
              </h3>
              {!showRating ? (
                <button
                  onClick={() => setShowRating(true)}
                  className="w-full p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Star className="w-5 h-5 text-yellow-400" />
                  <span>{language === 'ar' ? 'اترك تقييماً' : 'Leave a rating'}</span>
                </button>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <p className="text-gray-600 mb-3">
                      {language === 'ar' ? 'كيف كانت تجربتك؟' : 'How was your experience?'}
                    </p>
                    <RatingStars 
                      rating={rating} 
                      onRate={setRating} 
                      interactive 
                      size="lg" 
                    />
                  </div>
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder={language === 'ar' ? 'اترك تعليقاً (اختياري)' : 'Leave a comment (optional)'}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                  <div className="flex space-x-3 space-x-reverse">
                    <button
                      onClick={() => setShowRating(false)}
                      className="flex-1 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      {language === 'ar' ? 'إلغاء' : 'Cancel'}
                    </button>
                    <button
                      onClick={handleRateService}
                      disabled={rating === 0 || submittingRating}
                      className="flex-1 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                    >
                      {submittingRating
                        ? (language === 'ar' ? 'جاري الإرسال...' : 'Submitting...')
                        : (language === 'ar' ? 'إرسال التقييم' : 'Submit Rating')
                      }
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => navigate('/')}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-xl transition-colors"
            >
              {language === 'ar' ? 'العودة للرئيسية' : 'Back to Home'}
            </button>
            
            <button
              onClick={() => navigate('/bookings')}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 rounded-xl transition-colors"
            >
              {language === 'ar' ? 'عرض جميع الحجوزات' : 'View All Bookings'}
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BookingConfirmation;