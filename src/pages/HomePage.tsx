import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../contexts/AppContext';
import Layout from '../components/Layout';
import ServiceCard from '../components/ServiceCard';
import CategoryFilter from '../components/CategoryFilter';

const HomePage: React.FC = () => {
  const { language, services, user, profile } = useApp();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const navigate = useNavigate();

  const filteredServices = selectedCategory === 'all' 
    ? services 
    : services.filter(service => service.category === selectedCategory);

  const handleServiceSelect = (serviceId: string) => {
    if (!user || !profile) {
      navigate('/auth');
      return;
    }
    if (profile.role !== 'customer') {
      alert(language === 'ar' ? 'هذه الخدمة متاحة للعملاء فقط' : 'This service is for customers only');
      return;
    }
    navigate(`/booking/${serviceId}`);
  };

  const handleGetStarted = () => {
    if (!user || !profile) {
      navigate('/auth');
      return;
    }
    // Scroll to services section
    const servicesSection = document.getElementById('services-section');
    if (servicesSection) {
      servicesSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 px-4 py-8 text-white">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold leading-tight">
              {language === 'ar' ? 'احجز خدمتك في دقائق' : 'Book your service in minutes'}
            </h1>
            <p className="text-blue-100 max-w-md mx-auto">
              {language === 'ar' 
                ? 'خدمات منزلية عالية الجودة مع فنيين معتمدين في جميع أنحاء مصر'
                : 'High-quality home services with certified technicians across Egypt'
              }
            </p>
            <button
              onClick={handleGetStarted}
              className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 font-semibold px-8 py-3 rounded-xl transition-colors"
            >
              {language === 'ar' ? 'ابدأ الآن' : 'Get Started'}
            </button>
          </div>
        </div>

        {/* Welcome message for authenticated users */}
        {user && profile && (
          <div className="px-4">
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <h2 className="font-semibold text-blue-900 mb-1">
                {language === 'ar' ? `أهلاً ${profile.name}!` : `Welcome ${profile.name}!`}
              </h2>
              <p className="text-blue-700 text-sm">
                {profile.role === 'customer'
                  ? (language === 'ar' ? 'اختر الخدمة التي تحتاجها' : 'Choose the service you need')
                  : (language === 'ar' ? 'تحقق من لوحة التحكم للطلبات الجديدة' : 'Check your dashboard for new requests')
                }
              </p>
            </div>
          </div>
        )}

        {/* Category Filter */}
        <div className="px-4" id="services-section">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {language === 'ar' ? 'الخدمات' : 'Services'}
          </h2>
          <CategoryFilter 
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
        </div>

        {/* Services Grid */}
        <div className="px-4">
          {filteredServices.length > 0 ? (
            <div className="grid grid-cols-1 gap-3">
              {filteredServices.map((service) => (
                <ServiceCard
                  key={service.id}
                  service={service}
                  onSelect={handleServiceSelect}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {language === 'ar' ? 'لا توجد خدمات في هذه الفئة' : 'No services in this category'}
              </p>
            </div>
          )}
        </div>

        {/* Call to Action for non-authenticated users */}
        {!user && (
          <div className="px-4">
            <div className="bg-gray-50 rounded-xl p-6 text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {language === 'ar' ? 'جاهز للبدء؟' : 'Ready to get started?'}
              </h3>
              <p className="text-gray-600 mb-4">
                {language === 'ar' 
                  ? 'سجل دخولك لحجز الخدمات أو انضم كفني'
                  : 'Sign in to book services or join as a technician'
                }
              </p>
              <button
                onClick={() => navigate('/auth')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
              >
                {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default HomePage;