import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, ArrowLeft, Wrench, CheckCircle } from 'lucide-react';
import { signUp, signInWithEmail, signInWithGoogle } from '../lib/supabase';
import { useApp } from '../contexts/AppContext';

const TechnicianAuthPage: React.FC = () => {
  const { language } = useApp();
  const navigate = useNavigate();
  
  const [mode, setMode] = useState<'signin' | 'signup'>('signup');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (mode === 'signup') {
        if (formData.password !== formData.confirmPassword) {
          throw new Error(language === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match');
        }
        
        const { error } = await signUp(formData.email, formData.password, formData.name, 'technician');
        if (error) throw error;
        
        // Redirect to onboarding after successful signup
        navigate('/technician/onboarding');
      } else {
        const { error } = await signInWithEmail(formData.email, formData.password);
        if (error) throw error;

        // Will be handled by App.tsx routing
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError('');
    
    try {
      const { error } = await signInWithGoogle();
      if (error) throw error;
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const benefits = [
    {
      icon: CheckCircle,
      title: { ar: 'دخل إضافي', en: 'Extra Income' },
      description: { ar: 'احصل على دخل إضافي من خلال تقديم خدماتك', en: 'Earn extra income by providing your services' }
    },
    {
      icon: CheckCircle,
      title: { ar: 'مرونة في العمل', en: 'Flexible Schedule' },
      description: { ar: 'اعمل في الأوقات التي تناسبك', en: 'Work on your own schedule' }
    },
    {
      icon: CheckCircle,
      title: { ar: 'عملاء موثوقين', en: 'Trusted Customers' },
      description: { ar: 'تواصل مع عملاء موثوقين في منطقتك', en: 'Connect with verified customers in your area' }
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 to-blue-800">
      {/* Header */}
      <div className="p-4 flex items-center justify-between text-white">
        <button 
          onClick={() => navigate('/')}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6" />
        </button>
        <div className="flex items-center space-x-2 space-x-reverse">
          <Wrench className="w-6 h-6" />
          <span className="font-bold text-lg">
            {language === 'ar' ? 'انضم كفني' : 'Join as Technician'}
          </span>
        </div>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-8">
        {/* Benefits Section */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-white text-center mb-2">
            {language === 'ar' ? 'ابدأ رحلتك المهنية معنا' : 'Start Your Professional Journey'}
          </h1>
          <p className="text-blue-100 text-center mb-6">
            {language === 'ar' 
              ? 'انضم إلى شبكة الفنيين المحترفين وقدم خدماتك للعملاء'
              : 'Join our network of professional technicians and serve customers'
            }
          </p>
          
          <div className="space-y-4 mb-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="flex items-start space-x-3 space-x-reverse bg-white/10 p-4 rounded-lg">
                  <Icon className="w-6 h-6 text-green-300 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-white">{benefit.title[language]}</h3>
                    <p className="text-blue-100 text-sm">{benefit.description[language]}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Auth Form */}
        <div className="bg-white rounded-2xl p-6 shadow-xl">
          {/* Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1 mb-6">
            <button
              onClick={() => setMode('signup')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                mode === 'signup'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {language === 'ar' ? 'إنشاء حساب' : 'Sign Up'}
            </button>
            <button
              onClick={() => setMode('signin')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                mode === 'signin'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {mode === 'signup' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'}
                  required
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'ar' ? 'كلمة المرور' : 'Password'}
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                  placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter your password'}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {mode === 'signup' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm Password'}
                </label>
                <input
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={language === 'ar' ? 'أعد إدخال كلمة المرور' : 'Re-enter your password'}
                  required
                />
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              {loading 
                ? (language === 'ar' ? 'جاري التحميل...' : 'Loading...')
                : mode === 'signup'
                  ? (language === 'ar' ? 'إنشاء حساب فني' : 'Create Technician Account')
                  : (language === 'ar' ? 'تسجيل الدخول' : 'Sign In')
              }
            </button>
          </form>

          {/* Google Sign In */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {language === 'ar' ? 'أو' : 'Or'}
                </span>
              </div>
            </div>

            <button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="mt-4 w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              <img
                className="w-5 h-5 mr-2"
                src="https://www.google.com/favicon.ico"
                alt="Google"
              />
              {language === 'ar' ? 'المتابعة مع جوجل' : 'Continue with Google'}
            </button>
          </div>

          {/* Customer Link */}
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              {language === 'ar' ? 'تبحث عن خدمات؟' : 'Looking for services?'}
            </p>
            <button
              onClick={() => navigate('/auth')}
              className="text-blue-600 hover:text-blue-700 font-medium text-sm"
            >
              {language === 'ar' ? 'سجل كعميل' : 'Sign up as Customer'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechnicianAuthPage;
