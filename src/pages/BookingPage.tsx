import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Calendar, Clock, MapPin, CreditCard, ChevronLeft, ChevronRight } from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { createBooking } from '../lib/supabase';
import Layout from '../components/Layout';

const BookingPage: React.FC = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const { language, services, user, profile, isRTL } = useApp();
  const navigate = useNavigate();
  
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [address, setAddress] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const service = services.find(s => s.id === serviceId);

  useEffect(() => {
    if (!user || !profile) {
      navigate('/auth');
      return;
    }
    if (profile.role !== 'customer') {
      navigate('/');
      return;
    }
  }, [user, profile, navigate]);

  if (!service) {
    return (
      <Layout showNav={false}>
        <div className="p-4 text-center">
          <p className="text-gray-600">
            {language === 'ar' ? 'الخدمة غير موجودة' : 'Service not found'}
          </p>
        </div>
      </Layout>
    );
  }

  const timeSlots = [
    '09:00', '10:00', '11:00', '12:00',
    '14:00', '15:00', '16:00', '17:00'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !profile) {
      navigate('/auth');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const bookingData = {
        customer_id: user.id,
        service_id: service.id,
        address,
        notes,
        scheduled_date: selectedDate,
        scheduled_time: selectedTime,
        total_price: service.base_price,
        payment_method: 'cash' as const,
        status: 'pending' as const,
      };
      
      const { data, error } = await createBooking(bookingData);
      if (error) throw error;
      
      navigate(`/confirmation/${data.id}`);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const ChevronIcon = isRTL ? ChevronRight : ChevronLeft;

  return (
    <Layout showNav={false}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
          <button 
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronIcon className="w-5 h-5 text-gray-600" />
          </button>
          <h1 className="flex-1 text-center text-lg font-semibold text-gray-900">
            {language === 'ar' ? 'حجز الخدمة' : 'Book Service'}
          </h1>
          <div className="w-9" />
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-6">
          {/* Service Details */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h2 className="font-semibold text-gray-900 mb-3">
              {language === 'ar' ? service.name_ar : service.name_en}
            </h2>
            <p className="text-sm text-gray-600 mb-3">
              {language === 'ar' ? service.description_ar : service.description_en}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold text-blue-600">
                {service.base_price} {language === 'ar' ? 'ج.م' : 'EGP'}
              </span>
              <span className="text-sm text-gray-600">
                {service.duration_minutes} {language === 'ar' ? 'دقيقة' : 'minutes'}
              </span>
            </div>
          </div>

          {/* Date Selection */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-2 space-x-reverse mb-3">
              <Calendar className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">
                {language === 'ar' ? 'اختر التاريخ' : 'Select Date'}
              </h3>
            </div>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Time Selection */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-2 space-x-reverse mb-3">
              <Clock className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">
                {language === 'ar' ? 'اختر الوقت' : 'Select Time'}
              </h3>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {timeSlots.map((time) => (
                <button
                  key={time}
                  type="button"
                  onClick={() => setSelectedTime(time)}
                  className={`p-3 rounded-lg border text-sm font-medium transition-all ${
                    selectedTime === time
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-gray-50 text-gray-700 border-gray-200 hover:border-blue-300'
                  }`}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>

          {/* Address */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-2 space-x-reverse mb-3">
              <MapPin className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">
                {language === 'ar' ? 'العنوان' : 'Address'}
              </h3>
            </div>
            <textarea
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder={language === 'ar' ? 'أدخل عنوانك بالتفصيل...' : 'Enter your detailed address...'}
              rows={3}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              required
            />
          </div>

          {/* Notes */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-3">
              {language === 'ar' ? 'ملاحظات إضافية' : 'Additional Notes'}
            </h3>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={language === 'ar' ? 'أي ملاحظات أو تفاصيل إضافية...' : 'Any additional notes or details...'}
              rows={2}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>

          {/* Payment Method */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-2 space-x-reverse mb-3">
              <CreditCard className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">
                {language === 'ar' ? 'طريقة الدفع' : 'Payment Method'}
              </h3>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-700">
                {language === 'ar' ? 'الدفع عند الوصول (نقدي)' : 'Cash on arrival'}
              </span>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!selectedDate || !selectedTime || !address || loading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-4 rounded-xl transition-colors"
          >
            {loading
              ? (language === 'ar' ? 'جاري الحجز...' : 'Booking...')
              : (language === 'ar' ? 'تأكيد الحجز' : 'Confirm Booking')
            }
          </button>
        </form>
      </div>
    </Layout>
  );
};

export default BookingPage;