import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface Profile {
  id: string;
  name: string;
  phone?: string;
  role: 'customer' | 'technician';
  avatar_url?: string;
  rating: number;
  total_ratings: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  name_ar: string;
  name_en: string;
  description_ar?: string;
  description_en?: string;
  category: string;
  base_price: number;
  duration_minutes: number;
  icon: string;
  is_active: boolean;
  created_at: string;
}

export interface Booking {
  id: string;
  customer_id: string;
  technician_id?: string;
  service_id: string;
  address: string;
  notes?: string;
  scheduled_date: string;
  scheduled_time: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  total_price: number;
  payment_method: 'cash' | 'card' | 'wallet';
  created_at: string;
  updated_at: string;
  // Relations
  service?: Service;
  customer?: Profile;
  technician?: Profile;
}

export interface Rating {
  id: string;
  booking_id: string;
  customer_id: string;
  technician_id: string;
  rating: number;
  comment?: string;
  created_at: string;
}

// Auth helpers
export const signInWithGoogle = async () => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/auth/callback`
    }
  });
  return { data, error };
};

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  return { data, error };
};

export const signUpWithEmail = async (email: string, password: string, name: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name
      },
      emailRedirectTo: `${window.location.origin}/auth/callback`
    }
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

// Profile helpers
export const getProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  return { data, error };
};

export const createProfile = async (userId: string, name: string, role: 'customer' | 'technician' = 'customer') => {
  const { data, error } = await supabase
    .from('profiles')
    .insert({
      id: userId,
      name,
      role
    })
    .select()
    .single();
  return { data, error };
};

export const updateProfile = async (userId: string, updates: Partial<Profile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  return { data, error };
};

// Services helpers
export const getServices = async () => {
  const { data, error } = await supabase
    .from('services')
    .select('*')
    .eq('is_active', true)
    .order('name_ar');
  return { data, error };
};

// Bookings helpers
export const createBooking = async (booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('bookings')
    .insert(booking)
    .select(`
      *,
      service:services(*),
      customer:profiles!customer_id(*),
      technician:profiles!technician_id(*)
    `)
    .single();
  return { data, error };
};

export const getBookings = async (userId: string) => {
  const { data, error } = await supabase
    .from('bookings')
    .select(`
      *,
      service:services(*),
      customer:profiles!customer_id(*),
      technician:profiles!technician_id(*)
    `)
    .or(`customer_id.eq.${userId},technician_id.eq.${userId}`)
    .order('created_at', { ascending: false });
  return { data, error };
};

export const updateBooking = async (bookingId: string, updates: Partial<Booking>) => {
  const { data, error } = await supabase
    .from('bookings')
    .update(updates)
    .eq('id', bookingId)
    .select(`
      *,
      service:services(*),
      customer:profiles!customer_id(*),
      technician:profiles!technician_id(*)
    `)
    .single();
  return { data, error };
};

// Technicians helpers
export const getTechnicians = async () => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('role', 'technician')
    .eq('is_available', true)
    .order('rating', { ascending: false });
  return { data, error };
};

// Ratings helpers
export const createRating = async (rating: Omit<Rating, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('ratings')
    .insert(rating)
    .select()
    .single();
  return { data, error };
};