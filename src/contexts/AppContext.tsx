import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, Profile, Service, Booking, getServices, getProfile } from '../lib/supabase';

interface AppContextType {
  // Auth
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  
  // App state
  language: 'ar' | 'en';
  setLanguage: (lang: 'ar' | 'en') => void;
  isRTL: boolean;
  
  // Data
  services: Service[];
  bookings: Booking[];
  
  // Actions
  refreshProfile: () => Promise<void>;
  refreshServices: () => Promise<void>;
  refreshBookings: () => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const [services, setServices] = useState<Service[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);

  const isRTL = language === 'ar';

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      if (session?.user) {
        loadOrCreateProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        if (session?.user) {
          await loadOrCreateProfile(session.user.id);
        } else {
          setProfile(null);
          setBookings([]);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Load services on mount
  useEffect(() => {
    refreshServices();
  }, []);

  const loadOrCreateProfile = async (userId: string) => {
    try {
      const { data, error } = await getProfile(userId);
      
      if (error && error.code === 'PGRST116') {
        // Profile doesn't exist, create one
        const user = await supabase.auth.getUser();
        const userName = user.data.user?.user_metadata?.name || 
                        user.data.user?.email?.split('@')[0] || 
                        'User';
        
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            name: userName,
            role: 'customer'
          })
          .select()
          .single();
          
        if (createError) throw createError;
        setProfile(newProfile);
      } else if (error) {
        throw error;
      } else {
        setProfile(data);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await loadOrCreateProfile(user.id);
    }
  };

  const refreshServices = async () => {
    try {
      const { data, error } = await getServices();
      if (error) throw error;
      setServices(data || []);
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  const refreshBookings = async () => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          service:services(*),
          customer:profiles!customer_id(*),
          technician:profiles!technician_id(*)
        `)
        .or(`customer_id.eq.${user.id},technician_id.eq.${user.id}`)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      setBookings(data || []);
    } catch (error) {
      console.error('Error loading bookings:', error);
    }
  };

  // Load bookings when user changes
  useEffect(() => {
    if (user) {
      refreshBookings();
    }
  }, [user]);

  return (
    <AppContext.Provider value={{
      user,
      profile,
      loading,
      language,
      setLanguage,
      isRTL,
      services,
      bookings,
      refreshProfile,
      refreshServices,
      refreshBookings,
    }}>
      {children}
    </AppContext.Provider>
  );
};