@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom RTL styles */
.rtl {
  direction: rtl;
  text-align: right;
  font-family: 'Cairo', 'Segoe UI', system-ui, sans-serif;
}

.ltr {
  direction: ltr;
  text-align: left;
  font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
}

/* Custom scrollbar for RTL */
.rtl::-webkit-scrollbar {
  width: 6px;
}

.rtl::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.rtl::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Custom utility classes */
.space-x-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.space-x-2-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.space-x-3-reverse > * + * {
  margin-right: 0.75rem;
  margin-left: 0;
}

.space-x-4-reverse > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

/* Custom button hover effects */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors;
}

/* Service card animations */
.service-card {
  transform: translateY(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom color variables */
:root {
  --primary-blue: #2E86DE;
  --secondary-gold: #F1C40F;
  --success-green: #27AE60;
  --warning-orange: #F39C12;
  --error-red: #E74C3C;
}

/* Status badge styles */
.status-pending {
  background-color: rgba(241, 196, 15, 0.1);
  color: #F39C12;
}

.status-confirmed {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27AE60;
}

.status-completed {
  background-color: rgba(46, 134, 222, 0.1);
  color: #2E86DE;
}

/* Mobile-first responsive design helpers */
@media (max-width: 640px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  /* Add dark mode styles here if needed */
}